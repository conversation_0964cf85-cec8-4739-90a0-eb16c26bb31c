#===============================================================================
# Season Control Functions for Following Pokemon EX
# Allows manual setting of seasons for testing and gameplay purposes
#===============================================================================

#===============================================================================
# Global variable to override the season
#===============================================================================
class PokemonGlobalMetadata
  attr_accessor :season_override
end

#===============================================================================
# Enhanced season function that respects manual overrides
#===============================================================================
def pbGetCurrentSeason
  # If season override is set, use that instead of real time
  return $PokemonGlobal.season_override if $PokemonGlobal.season_override

  # Otherwise use the original time-based calculation
  return (pbGetTimeNow.mon - 1) % 4
end

# Alias the original function for compatibility
alias pbGetSeason_original pbGetSeason

# Override pbGetSeason to use the enhanced version
def pbGetSeason
  return pbGetCurrentSeason
end

#===============================================================================
# Season setting functions
#===============================================================================

# Set the season manually (0=Spring, 1=Summer, 2=Autumn, 3=Winter)
def pbSetSeason(season)
  return if season < 0 || season > 3

  old_season = pbGetCurrentSeason
  $PokemonGlobal.season_override = season

  # Update the last known season to trigger form updates
  $PokemonGlobal.last_season = old_season

  # Force update seasonal forms for Following Pokemon
  FollowingPkmn.update_seasonal_forms if defined?(FollowingPkmn)

  # Refresh the map if needed
  $game_map.need_refresh = true if $game_map

  season_name = pbGetSeasonName(season)
  pbMessage(_INTL("The season has been set to {1}!", season_name))
end

# Reset season to use real-world time
def pbResetSeason
  old_season = pbGetCurrentSeason
  $PokemonGlobal.season_override = nil

  # Update the last known season to trigger form updates
  $PokemonGlobal.last_season = old_season

  # Force update seasonal forms for Following Pokemon
  FollowingPkmn.update_seasonal_forms if defined?(FollowingPkmn)

  # Refresh the map if needed
  $game_map.need_refresh = true if $game_map

  current_season = pbGetCurrentSeason
  season_name = pbGetSeasonName(current_season)
  pbMessage(_INTL("Season has been reset to real-world time. Current season: {1}", season_name))
end

# Convenience methods for setting specific seasons
def pbSetSeasonSpring
  pbSetSeason(0)
end

def pbSetSeasonSummer
  pbSetSeason(1)
end

def pbSetSeasonAutumn
  pbSetSeason(2)
end

def pbSetSeasonWinter
  pbSetSeason(3)
end

# Check if season is manually overridden
def pbSeasonOverridden?
  return !$PokemonGlobal.season_override.nil?
end

# Get the real-world season (ignoring override)
def pbGetRealSeason
  return (pbGetTimeNow.mon - 1) % 4
end

#===============================================================================
# Debug menu integration
#===============================================================================
MenuHandlers.add(:debug_menu, :set_season, {
  "name"        => _INTL("Set Season"),
  "parent"      => :field_menu,
  "description" => _INTL("Change the current season for testing seasonal forms."),
  "effect"      => proc {
    current_season = pbGetSeason
    real_season = pbGetRealSeason
    overridden = pbSeasonOverridden?

    cmd = 0
    loop do
      season_names = [_INTL("Spring"), _INTL("Summer"), _INTL("Autumn"), _INTL("Winter")]

      cmds = []
      cmds.push(_INTL("Current season: {1}{2}", season_names[current_season],
                      overridden ? " (OVERRIDDEN)" : ""))
      cmds.push(_INTL("Real-world season: {1}", season_names[real_season]))
      cmds.push(_INTL("Set to Spring"))
      cmds.push(_INTL("Set to Summer"))
      cmds.push(_INTL("Set to Autumn"))
      cmds.push(_INTL("Set to Winter"))
      cmds.push(_INTL("Reset to real-world time"))

      cmd = pbShowCommands(nil, cmds, -1, cmd)
      break if cmd < 0

      case cmd
      when 0, 1  # Info display - do nothing
        next
      when 2  # Spring
        pbSetSeason(0)
        current_season = 0
      when 3  # Summer
        pbSetSeason(1)
        current_season = 1
      when 4  # Autumn
        pbSetSeason(2)
        current_season = 2
      when 5  # Winter
        pbSetSeason(3)
        current_season = 3
      when 6  # Reset
        pbResetSeason
        current_season = pbGetSeason
      end

      overridden = pbSeasonOverridden?
    end
  }
})
