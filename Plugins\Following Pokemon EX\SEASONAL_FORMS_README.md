# Seasonal Forms for Following Pokemon

This system automatically updates Following Pokemon sprites when their forms change due to seasonal changes.

## How it works

1. **Automatic Detection**: The system checks every step if the season has changed
2. **Form Update**: When a season change is detected, Pokemon with seasonal forms have their forms recalculated
3. **Sprite Refresh**: If the form changes, the Following Pokemon sprite is automatically updated

## Currently Supported Pokemon

- **Deerling** - Changes form based on season (Spring/Summer/Autumn/Winter)
- **Sawsbuck** - Changes form based on season (Spring/Summer/Autumn/Winter)

## Adding New Pokemon with Seasonal Forms

To add a new Pokemon with seasonal forms:

1. **Add the form handler** in `Plugins/Scripts/014_Pokemon/001_Pokemon-related/001_FormHandlers.rb`:
   ```ruby
   MultipleForms.register(:NEWPOKEMON, {
     "getForm" => proc { |pkmn|
       next pbGetSeason
     }
   })
   ```

2. **Add the species to the list** in `Plugins/Following Pokemon EX/Main Module/Event_Sprite Commands.rb`:
   ```ruby
   seasonal_species = [:DEERLING, :SAWSBUCK, :NEWPOKEMON]
   ```

3. **Ensure sprite files exist** for each seasonal form in the appropriate directories

## Manual Updates

You can manually trigger a seasonal form update by calling:
```ruby
FollowingPkmn.update_seasonal_forms
```

## Technical Details

- Season tracking is stored in `$PokemonGlobal.last_season`
- The system uses the existing `pbGetSeason` function which returns 0-3 for Spring/Summer/Autumn/Winter
- Form updates are triggered by accessing the Pokemon's `form` property, which automatically calls the form handler
- The system only updates sprites when the form actually changes to avoid unnecessary processing

## Event Handlers

The system uses these event handlers:
- `:on_player_step_taken` - Checks for seasonal changes every step
- `:on_enter_map` - Initializes season tracking when entering a new map
