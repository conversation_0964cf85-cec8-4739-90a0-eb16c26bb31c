#===============================================================================
# Test Script for Seasonal Forms
# This file contains test functions to verify seasonal form functionality
#===============================================================================

# Test function to cycle through all seasons and show the changes
def pbTestSeasonalForms
  return if !FollowingPkmn.active?
  
  following_pkmn = FollowingPkmn.get_pokemon
  if !following_pkmn
    pbMessage(_INTL("No Following Pokemon active!"))
    return
  end
  
  if !FollowingPkmn.has_seasonal_form?(following_pkmn)
    pbMessage(_INTL("{1} does not have seasonal forms.", following_pkmn.name))
    return
  end
  
  pbMessage(_INTL("Testing seasonal forms for {1}!", following_pkmn.name))
  
  original_season = pbGetSeason
  season_names = ["Spring", "Summer", "Autumn", "Winter"]
  
  # Cycle through all seasons
  4.times do |season|
    pbSetSeason(season)
    pbMessage(_INTL("Season set to {1}. {2} is now in form {3}.", 
                    season_names[season], following_pkmn.name, following_pkmn.form))
    pbWait(60)  # Wait 1 second
  end
  
  # Reset to original season
  if pbSeasonOverridden?
    pbResetSeason
  else
    pbSetSeason(original_season)
  end
  
  pbMessage(_INTL("Test complete! Season restored."))
end

# Quick function to give the player a Deerling for testing
def pbGiveTestDeerling
  if $player.party_full?
    pbMessage(_INTL("Your party is full!"))
    return
  end
  
  deerling = Pokemon.new(:DEERLING, 10)
  deerling.name = "Test Deerling"
  $player.party.push(deerling)
  
  pbMessage(_INTL("You received a test Deerling!"))
  pbMessage(_INTL("Use it as a Following Pokemon to test seasonal forms."))
end

# Quick function to give the player a Sawsbuck for testing
def pbGiveTestSawsbuck
  if $player.party_full?
    pbMessage(_INTL("Your party is full!"))
    return
  end
  
  sawsbuck = Pokemon.new(:SAWSBUCK, 34)
  sawsbuck.name = "Test Sawsbuck"
  $player.party.push(sawsbuck)
  
  pbMessage(_INTL("You received a test Sawsbuck!"))
  pbMessage(_INTL("Use it as a Following Pokemon to test seasonal forms."))
end

# Function to show current season information
def pbShowSeasonInfo
  current_season = pbGetSeason
  real_season = pbGetRealSeason
  overridden = pbSeasonOverridden?
  season_names = ["Spring", "Summer", "Autumn", "Winter"]
  
  msg = _INTL("Current Season: {1}", season_names[current_season])
  msg += _INTL("\\nReal-world Season: {1}", season_names[real_season])
  
  if overridden
    msg += _INTL("\\n\\nSeason is manually overridden!")
  else
    msg += _INTL("\\n\\nUsing real-world time.")
  end
  
  if FollowingPkmn.active?
    following_pkmn = FollowingPkmn.get_pokemon
    if following_pkmn && FollowingPkmn.has_seasonal_form?(following_pkmn)
      msg += _INTL("\\n\\nFollowing {1} (Form {2})", following_pkmn.name, following_pkmn.form)
    elsif following_pkmn
      msg += _INTL("\\n\\nFollowing {1} (No seasonal forms)", following_pkmn.name)
    end
  else
    msg += _INTL("\\n\\nNo Following Pokemon active.")
  end
  
  pbMessage(msg)
end

#===============================================================================
# Debug menu integration for testing
#===============================================================================
MenuHandlers.add(:debug_menu, :seasonal_forms_test, {
  "name"        => _INTL("Test Seasonal Forms"),
  "parent"      => :field_menu,
  "description" => _INTL("Test seasonal form functionality with Following Pokemon."),
  "effect"      => proc {
    cmd = 0
    loop do
      cmds = []
      cmds.push(_INTL("Show season information"))
      cmds.push(_INTL("Test seasonal form changes"))
      cmds.push(_INTL("Give test Deerling"))
      cmds.push(_INTL("Give test Sawsbuck"))
      
      cmd = pbShowCommands(nil, cmds, -1, cmd)
      break if cmd < 0
      
      case cmd
      when 0  # Show season info
        pbShowSeasonInfo
      when 1  # Test seasonal forms
        pbTestSeasonalForms
      when 2  # Give Deerling
        pbGiveTestDeerling
      when 3  # Give Sawsbuck
        pbGiveTestSawsbuck
      end
    end
  }
})
